"""
AI Agent Service for managing CrewAI agents and orchestrating research tasks.
"""
import asyncio
import json
import uuid
from typing import Dict, List, Optional, Any
from datetime import datetime
# Temporarily disable complex imports to get basic API working
# from crewai import Agent, Task, Crew, Process
# from langchain.memory import ConversationBufferMemory
# from langchain.schema import BaseMessage
from sqlalchemy.orm import Session

from services.llm_service import llm_service
# from tools.web_automation import web_search_tool, web_form_tool, web_scraping_tool
from models import ResearchSession, ResearchFinding, UserSettings
from database import get_db
import logging

logger = logging.getLogger(__name__)

class AgentService:
    """
    Service for managing AI agents and orchestrating research tasks.
    Simplified version for basic API functionality.
    """

    def __init__(self):
        self.active_sessions: Dict[str, Dict] = {}
        # self.memory_store: Dict[str, ConversationBufferMemory] = {}

    async def start_research_session(self, query: str, user_id: str = "default") -> str:
        """
        Start a new research session using real Gemini API.

        Args:
            query: Research query
            user_id: User identifier

        Returns:
            Session ID
        """
        session_id = str(uuid.uuid4())

        # Create a session in memory
        session_data = {
            "id": session_id,
            "session_id": session_id,  # Add for compatibility with ResearchStatusResponse
            "query": query,
            "user_id": user_id,
            "status": "running",
            "created_at": datetime.now().isoformat(),
            "start_time": datetime.now().isoformat(),  # Add for ResearchStatusResponse
            "progress": {"stage": "starting", "percentage": 0},  # Add for ResearchStatusResponse
            "results": []
        }

        self.active_sessions[session_id] = session_data

        # Also save to database
        try:
            db = next(get_db())
            db_session = ResearchSession(
                title=f"Research: {query[:50]}...",  # Truncate title if too long
                description=f"AI research session for: {query}",
                query=query,
                user_id=int(user_id) if user_id.isdigit() else 1,  # Default to user 1 for non-numeric IDs
                status="in_progress",
                priority="normal",
                context_data={"session_uuid": session_id}  # Store UUID for linking
            )
            db.add(db_session)
            db.commit()
            db.refresh(db_session)

            # Store the database ID in memory session for reference
            session_data["db_id"] = db_session.id

            logger.info(f"Created database session {db_session.id} for UUID {session_id}")

        except Exception as e:
            logger.error(f"Failed to save session to database: {e}")
            # Continue with in-memory session even if DB save fails
        finally:
            db.close()

        try:
            # Use real LLM service to generate research content
            research_prompt = f"""
            You are an AI research assistant specializing in aerospace and industrial parts sourcing.

            Research Query: {query}

            Please provide a comprehensive research response that includes:
            1. Key findings related to the query
            2. Potential suppliers or sources
            3. Technical specifications if applicable
            4. Market insights and pricing considerations
            5. Recommendations for next steps

            Format your response in a clear, structured manner.
            """

            response = await llm_service.generate_content(
                prompt=research_prompt,
                model="gemini-2.0-flash-001"
            )

            if response["status"] == "success":
                session_data["results"].append({
                    "type": "ai_research",
                    "content": response["content"],
                    "timestamp": datetime.now().isoformat(),
                    "model_used": response["model"],
                    "token_usage": response.get("usage", {})
                })
                session_data["status"] = "completed"
                session_data["progress"] = {"stage": "completed", "percentage": 100}
                session_data["result"] = response["content"]

                # Update database session
                self._update_db_session(session_data, "completed", response["content"])
            else:
                session_data["results"].append({
                    "type": "error",
                    "content": f"Research failed: {response.get('error', 'Unknown error')}",
                    "timestamp": datetime.now().isoformat()
                })
                session_data["status"] = "failed"
                session_data["progress"] = {"stage": "failed", "percentage": 0}
                session_data["error"] = response.get('error', 'Unknown error')

                # Update database session
                self._update_db_session(session_data, "failed", None)

        except Exception as e:
            logger.error(f"Research session failed: {e}")
            session_data["results"].append({
                "type": "error",
                "content": f"Research session failed: {str(e)}",
                "timestamp": datetime.now().isoformat()
            })
            session_data["status"] = "failed"
            session_data["progress"] = {"stage": "failed", "percentage": 0}
            session_data["error"] = str(e)

            # Update database session
            self._update_db_session(session_data, "failed", None)

        return session_id

    def get_session_status(self, session_id: str) -> Optional[Dict]:
        """
        Get the status of a research session.

        Args:
            session_id: Session identifier

        Returns:
            Session status data or None if not found
        """
        return self.active_sessions.get(session_id)

    def get_all_sessions(self, user_id: str = "default") -> List[Dict]:
        """
        Get all sessions for a user.

        Args:
            user_id: User identifier

        Returns:
            List of session data
        """
        return [
            session for session in self.active_sessions.values()
            if session.get("user_id") == user_id
        ]

    def _update_db_session(self, session_data: Dict, status: str, result: Optional[str] = None):
        """
        Update the database session with completion status and results.

        Args:
            session_data: In-memory session data
            status: New status (completed, failed)
            result: Research result content (if successful)
        """
        try:
            if "db_id" not in session_data:
                logger.warning(f"No database ID found for session {session_data.get('session_id')}")
                return

            db = next(get_db())
            db_session = db.query(ResearchSession).filter(
                ResearchSession.id == session_data["db_id"]
            ).first()

            if db_session:
                db_session.status = status
                db_session.research_results = {
                    "content": result,
                    "results": session_data.get("results", []),
                    "progress": session_data.get("progress", {}),
                    "session_uuid": session_data.get("session_id")
                }
                if status == "completed":
                    db_session.completed_at = datetime.utcnow()

                db.commit()
                logger.info(f"Updated database session {db_session.id} with status {status}")
            else:
                logger.warning(f"Database session {session_data['db_id']} not found")

        except Exception as e:
            logger.error(f"Failed to update database session: {e}")
        finally:
            db.close()

    async def test_connection(self) -> Dict[str, Any]:
        """
        Test the connection to LLM services.

        Returns:
            Connection test results
        """
        try:
            # Test LLM service connection
            result = await llm_service.test_connection()
            return {
                "status": "success",
                "llm_service": result,
                "message": "All services connected successfully"
            }
        except Exception as e:
            return {
                "status": "error",
                "message": f"Connection test failed: {str(e)}"
            }

# Create global instance
agent_service = AgentService()
