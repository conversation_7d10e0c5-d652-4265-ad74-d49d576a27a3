"""
Research session routes for Expendra API.
"""
import asyncio
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from sqlalchemy.orm import Session
from pydantic import BaseModel

from database import get_db
from models import User, UserSettings, ResearchSession, ResearchFinding
from schemas import MessageResponse
from auth import get_current_active_user
from services.agent_service import agent_service
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/research", tags=["Research Sessions"])

# Request/Response Models
class StartResearchRequest(BaseModel):
    query: str
    context_data: Optional[dict] = None
    priority: str = "normal"  # low, normal, high, urgent
    research_depth: str = "standard"  # quick, standard, deep

class ResearchSessionResponse(BaseModel):
    id: int
    title: str
    description: str
    query: str
    status: str
    priority: str
    created_at: str
    updated_at: str
    completed_at: Optional[str] = None
    estimated_duration: Optional[int] = None
    actual_duration: Optional[int] = None
    
    class Config:
        from_attributes = True

class ResearchStatusResponse(BaseModel):
    session_id: str
    status: str
    progress: dict
    start_time: str
    query: str
    result: Optional[str] = None
    error: Optional[str] = None

class ResearchFindingResponse(BaseModel):
    id: int
    finding_type: str
    title: str
    description: Optional[str]
    data: dict
    source_url: Optional[str]
    source_name: Optional[str]
    confidence_score: int
    created_at: str
    
    class Config:
        from_attributes = True

@router.post("/start", response_model=dict)
async def start_research_session(
    request: StartResearchRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Start a new AI-powered research session.
    """
    try:
        # Get user settings
        user_settings = db.query(UserSettings).filter(
            UserSettings.user_id == current_user.id
        ).first()
        
        if not user_settings:
            # Create default settings if they don't exist
            user_settings = UserSettings(
                user_id=current_user.id,
                default_llm_provider="google",  # Default to Google Gemini
                default_model="gemini-pro"
            )
            db.add(user_settings)
            db.commit()
            db.refresh(user_settings)
        
        # Validate that we have the necessary API key
        api_key_field = f"{user_settings.default_llm_provider}_api_key"
        api_key = getattr(user_settings, api_key_field, None)
        
        if not api_key and user_settings.default_llm_provider != "ollama":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"No API key configured for {user_settings.default_llm_provider}. Please update your settings."
            )
        
        # Start the research session (simplified call to match agent service signature)
        session_id = await agent_service.start_research_session(
            query=request.query,
            user_id=str(current_user.id)
        )
        
        logger.info(f"Started research session {session_id} for user {current_user.id}")
        
        return {
            "session_id": session_id,
            "status": "started",
            "message": "Research session started successfully",
            "query": request.query
        }
        
    except Exception as e:
        logger.error(f"Error starting research session: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to start research session: {str(e)}"
        )

@router.get("/sessions", response_model=List[ResearchSessionResponse])
async def get_user_research_sessions(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
    limit: int = 20,
    offset: int = 0
):
    """
    Get user's research sessions.
    """
    sessions = db.query(ResearchSession).filter(
        ResearchSession.user_id == current_user.id
    ).order_by(ResearchSession.created_at.desc()).offset(offset).limit(limit).all()
    
    return sessions

@router.get("/sessions/{session_id}", response_model=ResearchSessionResponse)
async def get_research_session(
    session_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Get details of a specific research session.
    """
    session = db.query(ResearchSession).filter(
        ResearchSession.id == session_id,
        ResearchSession.user_id == current_user.id
    ).first()
    
    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Research session not found"
        )
    
    return session

@router.get("/sessions/{session_id}/status", response_model=ResearchStatusResponse)
async def get_research_session_status(
    session_id: str,
    current_user: User = Depends(get_current_active_user)
):
    """
    Get real-time status of a research session.
    """
    try:
        status_info = agent_service.get_session_status(session_id)
        
        if "error" in status_info:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=status_info["error"]
            )
        
        return ResearchStatusResponse(**status_info)
        
    except Exception as e:
        logger.error(f"Error getting session status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get session status: {str(e)}"
        )

@router.get("/sessions/{session_id}/findings", response_model=List[ResearchFindingResponse])
async def get_research_findings(
    session_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Get findings from a research session.
    """
    # Verify session belongs to user
    session = db.query(ResearchSession).filter(
        ResearchSession.id == session_id,
        ResearchSession.user_id == current_user.id
    ).first()
    
    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Research session not found"
        )
    
    findings = db.query(ResearchFinding).filter(
        ResearchFinding.session_id == session_id
    ).order_by(ResearchFinding.created_at.desc()).all()
    
    return findings

@router.delete("/sessions/{session_id}", response_model=MessageResponse)
async def delete_research_session(
    session_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Delete a research session and its findings.
    """
    # Verify session belongs to user
    session = db.query(ResearchSession).filter(
        ResearchSession.id == session_id,
        ResearchSession.user_id == current_user.id
    ).first()
    
    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Research session not found"
        )
    
    # Delete findings first (due to foreign key constraint)
    db.query(ResearchFinding).filter(
        ResearchFinding.session_id == session_id
    ).delete()
    
    # Delete session
    db.delete(session)
    db.commit()
    
    return MessageResponse(
        message="Research session deleted successfully",
        success=True
    )

@router.post("/sessions/{session_id}/stop", response_model=MessageResponse)
async def stop_research_session(
    session_id: str,
    current_user: User = Depends(get_current_active_user)
):
    """
    Stop a running research session.
    """
    try:
        # Check if session exists and is running
        status_info = agent_service.get_session_status(session_id)
        
        if "error" in status_info:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=status_info["error"]
            )
        
        if status_info["status"] not in ["in_progress", "pending"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Session is not currently running"
            )
        
        # Stop the session (implementation would depend on how we track running sessions)
        # For now, we'll mark it as stopped in our active sessions
        if session_id in agent_service.active_sessions:
            agent_service.active_sessions[session_id]["status"] = "stopped"
        
        return MessageResponse(
            message="Research session stopped successfully",
            success=True
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error stopping session: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to stop session: {str(e)}"
        )

@router.get("/test-connection-public", response_model=dict)
async def test_connection_public():
    """
    Public endpoint to test the research service connection.
    """
    try:
        result = await agent_service.test_connection()
        return result
    except Exception as e:
        logger.error(f"Connection test failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Connection test failed: {str(e)}"
        )

@router.post("/start-public", response_model=dict)
async def start_research_session_public(request: StartResearchRequest):
    """
    Public endpoint to start a research session (for testing).
    """
    try:
        session_id = await agent_service.start_research_session(
            query=request.query,
            user_id="public_test"
        )

        return {
            "session_id": session_id,
            "status": "started",
            "message": "Research session started successfully"
        }
    except Exception as e:
        logger.error(f"Error starting research session: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to start research session: {str(e)}"
        )

@router.get("/sessions/{session_id}/status-public", response_model=dict)
async def get_session_status_public(session_id: str):
    """
    Public endpoint to get research session status (for testing).
    """
    try:
        status_data = agent_service.get_session_status(session_id)
        if not status_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Session not found"
            )

        return status_data
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting session status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get session status: {str(e)}"
        )

@router.get("/test-connection", response_model=dict)
async def test_llm_connection(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Test connection to the user's configured LLM provider.
    """
    try:
        # Get user settings
        user_settings = db.query(UserSettings).filter(
            UserSettings.user_id == current_user.id
        ).first()
        
        if not user_settings:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User settings not found"
            )
        
        # Test connection
        from services.llm_service import llm_service
        
        api_key_field = f"{user_settings.default_llm_provider}_api_key"
        api_key = getattr(user_settings, api_key_field, None)
        
        success = llm_service.test_connection(
            provider=user_settings.default_llm_provider,
            model=user_settings.default_model,
            api_key=api_key
        )
        
        return {
            "provider": user_settings.default_llm_provider,
            "model": user_settings.default_model,
            "connection_successful": success,
            "message": "Connection successful" if success else "Connection failed"
        }
        
    except Exception as e:
        logger.error(f"Error testing LLM connection: {e}")
        return {
            "connection_successful": False,
            "message": f"Connection test failed: {str(e)}"
        }
